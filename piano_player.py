"""
Piano player class for handling song playback and timing.
"""
import json
import time
import threading
from audio_engine import AudioEngine

class PianoPlayer:
    def __init__(self):
        self.audio_engine = AudioEngine()
        self.current_song = None
        self.is_playing = False
        self.is_paused = False
        self.playback_thread = None
        self.tempo_multiplier = 1.0
        self.current_note_index = 0
        
    def load_song(self, file_path):
        """Load a song from JSON file."""
        try:
            with open(file_path, 'r') as f:
                self.current_song = json.load(f)
            self.current_note_index = 0
            return True, f"Loaded: {self.current_song.get('title', 'Unknown')}"
        except Exception as e:
            return False, f"Error loading song: {str(e)}"
    
    def play_song(self, progress_callback=None):
        """Start playing the current song."""
        if not self.current_song:
            return False, "No song loaded"
        
        if self.is_playing:
            return False, "Already playing"
        
        self.is_playing = True
        self.is_paused = False
        
        def playback_worker():
            notes = self.current_song.get('notes', [])
            base_tempo = self.current_song.get('tempo', 120)
            
            # Calculate timing based on tempo (beats per minute)
            beat_duration = 60.0 / base_tempo / self.tempo_multiplier
            
            start_time = time.time()
            
            for i, note in enumerate(notes):
                if not self.is_playing:
                    break
                
                # Handle pause
                while self.is_paused and self.is_playing:
                    time.sleep(0.1)
                
                if not self.is_playing:
                    break
                
                self.current_note_index = i
                
                # Calculate when this note should play
                note_time = note.get('delay', 0) * beat_duration
                target_time = start_time + note_time
                
                # Wait until it's time to play this note
                current_time = time.time()
                if current_time < target_time:
                    time.sleep(target_time - current_time)
                
                # Play the note
                key = note.get('key', '')
                duration = note.get('duration', 0.5) * beat_duration
                
                if key:
                    self.audio_engine.play_key_async(key, duration)
                
                # Update progress
                if progress_callback:
                    progress = (i + 1) / len(notes) * 100
                    progress_callback(progress, key, i + 1, len(notes))
            
            self.is_playing = False
            self.current_note_index = 0
            if progress_callback:
                progress_callback(100, '', len(notes), len(notes))
        
        self.playback_thread = threading.Thread(target=playback_worker)
        self.playback_thread.daemon = True
        self.playback_thread.start()
        
        return True, "Playback started"
    
    def pause_song(self):
        """Pause the current song."""
        if self.is_playing and not self.is_paused:
            self.is_paused = True
            return True, "Paused"
        return False, "Not playing or already paused"
    
    def resume_song(self):
        """Resume the paused song."""
        if self.is_playing and self.is_paused:
            self.is_paused = False
            return True, "Resumed"
        return False, "Not paused"
    
    def stop_song(self):
        """Stop the current song."""
        if self.is_playing:
            self.is_playing = False
            self.is_paused = False
            self.current_note_index = 0
            return True, "Stopped"
        return False, "Not playing"
    
    def set_tempo(self, multiplier):
        """Set tempo multiplier (1.0 = normal, 2.0 = double speed, 0.5 = half speed)."""
        self.tempo_multiplier = max(0.1, min(3.0, multiplier))
        return True, f"Tempo set to {self.tempo_multiplier:.1f}x"
    
    def play_key_manually(self, key):
        """Play a single key manually."""
        return self.audio_engine.play_key(key)
    
    def get_song_info(self):
        """Get information about the current song."""
        if not self.current_song:
            return "No song loaded"
        
        title = self.current_song.get('title', 'Unknown')
        tempo = self.current_song.get('tempo', 120)
        note_count = len(self.current_song.get('notes', []))
        
        return f"Title: {title}\nTempo: {tempo} BPM\nNotes: {note_count}"
    
    def get_available_keys(self):
        """Get list of available piano keys."""
        return self.audio_engine.get_available_keys()
    
    def cleanup(self):
        """Clean up resources."""
        self.stop_song()
        self.audio_engine.cleanup()
