"""
Piano Autoplay - Main GUI Application
A program that automatically plays pre-composed songs by simulating piano key presses.
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from piano_player import PianoPlayer

class PianoAutoplayGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Piano Autoplay")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        self.piano_player = PianoPlayer()
        self.setup_ui()
        
        # Bind cleanup to window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🎹 Piano Autoplay", 
                               font=('Arial', 20, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Song controls frame
        controls_frame = ttk.LabelFrame(main_frame, text="Song Controls", padding="10")
        controls_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        controls_frame.columnconfigure(1, weight=1)
        
        # Load song button
        ttk.Button(controls_frame, text="Load Song", 
                  command=self.load_song).grid(row=0, column=0, padx=(0, 10))
        
        # Song info label
        self.song_info_var = tk.StringVar(value="No song loaded")
        self.song_info_label = ttk.Label(controls_frame, textvariable=self.song_info_var)
        self.song_info_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Playback controls
        playback_frame = ttk.Frame(controls_frame)
        playback_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        self.play_button = ttk.Button(playback_frame, text="▶ Play", 
                                     command=self.play_song)
        self.play_button.grid(row=0, column=0, padx=(0, 5))
        
        self.pause_button = ttk.Button(playback_frame, text="⏸ Pause", 
                                      command=self.pause_song, state='disabled')
        self.pause_button.grid(row=0, column=1, padx=(0, 5))
        
        self.stop_button = ttk.Button(playback_frame, text="⏹ Stop", 
                                     command=self.stop_song, state='disabled')
        self.stop_button.grid(row=0, column=2, padx=(0, 5))
        
        # Tempo control
        tempo_frame = ttk.LabelFrame(main_frame, text="Tempo Control", padding="10")
        tempo_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(tempo_frame, text="Speed:").grid(row=0, column=0, padx=(0, 10))
        
        self.tempo_var = tk.DoubleVar(value=1.0)
        self.tempo_scale = ttk.Scale(tempo_frame, from_=0.5, to=2.0, 
                                    variable=self.tempo_var, orient='horizontal',
                                    command=self.on_tempo_change)
        self.tempo_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        tempo_frame.columnconfigure(1, weight=1)
        
        self.tempo_label = ttk.Label(tempo_frame, text="1.0x")
        self.tempo_label.grid(row=0, column=2)
        
        # Progress bar
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.progress_label = tk.StringVar(value="Ready")
        ttk.Label(progress_frame, textvariable=self.progress_label).grid(row=1, column=0)
        
        # Virtual piano keyboard
        self.setup_piano_keyboard(main_frame)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief='sunken', anchor='w')
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def setup_piano_keyboard(self, parent):
        """Set up the virtual piano keyboard."""
        keyboard_frame = ttk.LabelFrame(parent, text="Virtual Piano Keyboard", padding="10")
        keyboard_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # White keys
        white_keys = ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L']
        white_key_notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B', 'C', 'D']
        
        white_frame = ttk.Frame(keyboard_frame)
        white_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        self.key_buttons = {}
        
        for i, (key, note) in enumerate(zip(white_keys, white_key_notes)):
            btn = tk.Button(white_frame, text=f"{key}\n{note}", 
                           bg='white', fg='black', width=4, height=3,
                           command=lambda k=key: self.play_key_manually(k))
            btn.grid(row=0, column=i, padx=1)
            self.key_buttons[key] = btn
        
        # Black keys
        black_keys = ['W', 'E', '', 'T', 'Y', 'U', '', 'O', 'P']
        black_key_notes = ['C#', 'D#', '', 'F#', 'G#', 'A#', '', 'C#', 'D#']
        
        black_frame = ttk.Frame(keyboard_frame)
        black_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        for i, (key, note) in enumerate(zip(black_keys, black_key_notes)):
            if key:  # Skip empty positions
                btn = tk.Button(black_frame, text=f"{key}\n{note}", 
                               bg='black', fg='white', width=3, height=2,
                               command=lambda k=key: self.play_key_manually(k))
                btn.grid(row=0, column=i, padx=1, sticky='w')
                # Offset black keys to align with white keys
                btn.grid_configure(padx=(25 + i*41, 1))
                self.key_buttons[key] = btn
    
    def load_song(self):
        """Load a song file."""
        file_path = filedialog.askopenfilename(
            title="Select Song File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir="./sample_songs" if os.path.exists("./sample_songs") else "."
        )
        
        if file_path:
            success, message = self.piano_player.load_song(file_path)
            if success:
                self.song_info_var.set(self.piano_player.get_song_info())
                self.status_var.set(message)
                self.play_button.config(state='normal')
            else:
                messagebox.showerror("Error", message)
                self.status_var.set("Failed to load song")
    
    def play_song(self):
        """Start playing the song."""
        success, message = self.piano_player.play_song(self.update_progress)
        if success:
            self.play_button.config(state='disabled')
            self.pause_button.config(state='normal')
            self.stop_button.config(state='normal')
            self.status_var.set("Playing...")
        else:
            messagebox.showerror("Error", message)
    
    def pause_song(self):
        """Pause/resume the song."""
        if self.piano_player.is_paused:
            success, message = self.piano_player.resume_song()
            if success:
                self.pause_button.config(text="⏸ Pause")
                self.status_var.set("Playing...")
        else:
            success, message = self.piano_player.pause_song()
            if success:
                self.pause_button.config(text="▶ Resume")
                self.status_var.set("Paused")
    
    def stop_song(self):
        """Stop the song."""
        success, message = self.piano_player.stop_song()
        if success:
            self.play_button.config(state='normal')
            self.pause_button.config(state='disabled', text="⏸ Pause")
            self.stop_button.config(state='disabled')
            self.progress_var.set(0)
            self.progress_label.set("Stopped")
            self.status_var.set("Stopped")
            self.reset_key_colors()
    
    def on_tempo_change(self, value):
        """Handle tempo change."""
        tempo = float(value)
        self.piano_player.set_tempo(tempo)
        self.tempo_label.config(text=f"{tempo:.1f}x")
    
    def play_key_manually(self, key):
        """Play a key manually."""
        self.piano_player.play_key_manually(key)
        self.highlight_key(key)
        self.root.after(200, lambda: self.reset_key_color(key))
    
    def highlight_key(self, key):
        """Highlight a key when played."""
        if key in self.key_buttons:
            original_bg = self.key_buttons[key]['bg']
            self.key_buttons[key].config(bg='yellow')
    
    def reset_key_color(self, key):
        """Reset key color."""
        if key in self.key_buttons:
            if key in ['W', 'E', 'T', 'Y', 'U', 'O', 'P']:
                self.key_buttons[key].config(bg='black')
            else:
                self.key_buttons[key].config(bg='white')
    
    def reset_key_colors(self):
        """Reset all key colors."""
        for key in self.key_buttons:
            self.reset_key_color(key)
    
    def update_progress(self, progress, current_key, current_note, total_notes):
        """Update progress bar and highlight current key."""
        self.progress_var.set(progress)
        self.progress_label.set(f"Note {current_note}/{total_notes}")
        
        if current_key:
            self.reset_key_colors()
            self.highlight_key(current_key)
        
        if progress >= 100:
            self.stop_song()
    
    def on_closing(self):
        """Handle window closing."""
        self.piano_player.cleanup()
        self.root.destroy()

def main():
    root = tk.Tk()
    app = PianoAutoplayGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
