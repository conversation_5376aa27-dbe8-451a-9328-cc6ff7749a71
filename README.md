# 🎹 Piano Autoplay

A Python program that automatically plays pre-composed songs by simulating piano key presses with a beautiful GUI interface.

## Features

- **Visual Piano Keyboard**: Interactive virtual piano with white and black keys
- **Song Autoplay**: Load and automatically play pre-composed songs
- **Tempo Control**: Adjust playback speed from 0.5x to 2.0x
- **Real-time Progress**: Visual progress bar and key highlighting during playback
- **Manual Play**: Click keys manually to play individual notes
- **Pause/Resume**: Full playback control with pause and resume functionality

## Keyboard Layout

The program uses a QWERTY keyboard layout mapped to piano keys:

### White Keys (Lower Octave)
- `A` → C4 (261.63 Hz)
- `S` → D4 (293.66 Hz)
- `D` → E4 (329.63 Hz)
- `F` → F4 (349.23 Hz)
- `G` → G4 (392.00 Hz)
- `H` → A4 (440.00 Hz)
- `J` → B4 (493.88 Hz)
- `K` → C5 (523.25 Hz)
- `L` → D5 (587.33 Hz)

### Black Keys (Sharps/Flats)
- `W` → C#4 (277.18 Hz)
- `E` → D#4 (311.13 Hz)
- `T` → F#4 (369.99 Hz)
- `Y` → G#4 (415.30 Hz)
- `U` → A#4 (466.16 Hz)
- `O` → C#5 (554.37 Hz)
- `P` → D#5 (622.25 Hz)

## Installation

1. **Install Python 3.7+** (if not already installed)

2. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Song Format

Songs are stored as JSON files with the following structure:

```json
{
  "title": "Song Name",
  "tempo": 120,
  "notes": [
    {"key": "A", "duration": 0.5, "delay": 0.0},
    {"key": "S", "duration": 0.5, "delay": 0.5},
    {"key": "D", "duration": 1.0, "delay": 1.0}
  ]
}
```

### Fields:
- **title**: Display name of the song
- **tempo**: Beats per minute (affects timing calculations)
- **notes**: Array of note objects
  - **key**: Keyboard key to press (A-L for white keys, W,E,T,Y,U,O,P for black keys)
  - **duration**: How long to hold the note (in beats)
  - **delay**: When to start the note (in beats from song start)

## Sample Songs

The program includes several sample songs in the `sample_songs/` directory:

- **Twinkle Twinkle Little Star** (`twinkle_twinkle.json`)
- **Mary Had a Little Lamb** (`mary_had_a_little_lamb.json`)
- **Happy Birthday** (`happy_birthday.json`)
- **C Major Scale Demo** (`scale_demo.json`)

## Usage

1. **Launch the application**: Run `python main.py`
2. **Load a song**: Click "Load Song" and select a JSON file
3. **Play the song**: Click the "▶ Play" button
4. **Control playback**: Use Pause/Resume and Stop buttons
5. **Adjust tempo**: Use the speed slider to change playback speed
6. **Manual play**: Click on the virtual piano keys to play notes manually

## Creating Your Own Songs

1. Create a new JSON file following the song format above
2. Use the keyboard layout reference to map your melody
3. Set appropriate timing with `delay` and `duration` values
4. Test your song by loading it in the application

## Technical Details

- **Audio Engine**: Uses pygame for real-time audio synthesis
- **Sound Generation**: Creates piano-like tones with harmonics and ADSR envelope
- **Threading**: Playback runs in separate thread for responsive UI
- **GUI Framework**: Built with tkinter for cross-platform compatibility

## Requirements

- Python 3.7+
- pygame 2.0.0+
- numpy 1.20.0+
- tkinter (usually included with Python)

## Troubleshooting

**Audio Issues**: Make sure your system audio is working and pygame can access audio devices.

**Performance**: For better audio quality, ensure your system isn't under heavy load during playback.

**File Loading**: Make sure song files are valid JSON and follow the correct format.

## License

This project is open source and available under the MIT License.
