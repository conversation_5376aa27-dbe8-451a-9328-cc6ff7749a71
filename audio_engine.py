"""
Audio engine for generating piano sounds using pygame.
"""
import pygame
import numpy as np
import math
import threading
import time

class AudioEngine:
    def __init__(self):
        pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
        pygame.mixer.init()
        
        # Piano key frequencies (A4 = 440 Hz)
        self.key_frequencies = {
            # White keys (lower octave)
            'A': 261.63,  # C4
            'S': 293.66,  # D4
            'D': 329.63,  # E4
            'F': 349.23,  # F4
            'G': 392.00,  # G4
            'H': 440.00,  # A4
            'J': 493.88,  # B4
            'K': 523.25,  # C5
            'L': 587.33,  # D5
            
            # Black keys
            'W': 277.18,  # C#4
            'E': 311.13,  # D#4
            'T': 369.99,  # F#4
            'Y': 415.30,  # G#4
            'U': 466.16,  # A#4
            'O': 554.37,  # C#5
            'P': 622.25,  # D#5
        }
        
        self.sample_rate = 22050
        self.duration = 0.5  # Default note duration
        
    def generate_tone(self, frequency, duration=0.5, volume=0.3):
        """Generate a piano-like tone for the given frequency."""
        frames = int(duration * self.sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            # Create a more piano-like sound with harmonics
            time_point = float(i) / self.sample_rate
            
            # Fundamental frequency
            wave = np.sin(2 * np.pi * frequency * time_point)
            
            # Add harmonics for richer sound
            wave += 0.3 * np.sin(2 * np.pi * frequency * 2 * time_point)  # Octave
            wave += 0.2 * np.sin(2 * np.pi * frequency * 3 * time_point)  # Fifth
            wave += 0.1 * np.sin(2 * np.pi * frequency * 4 * time_point)  # Double octave
            
            # Apply envelope (attack, decay, sustain, release)
            envelope = self._calculate_envelope(i, frames)
            wave *= envelope
            
            # Apply volume and convert to 16-bit
            wave = wave * volume * 32767
            arr[i] = [wave, wave]  # Stereo
            
        return arr.astype(np.int16)
    
    def _calculate_envelope(self, current_frame, total_frames):
        """Calculate ADSR envelope for more realistic piano sound."""
        attack_frames = int(0.1 * total_frames)
        decay_frames = int(0.2 * total_frames)
        release_frames = int(0.3 * total_frames)
        
        if current_frame < attack_frames:
            # Attack phase
            return current_frame / attack_frames
        elif current_frame < attack_frames + decay_frames:
            # Decay phase
            decay_progress = (current_frame - attack_frames) / decay_frames
            return 1.0 - (0.3 * decay_progress)  # Decay to 70% volume
        elif current_frame < total_frames - release_frames:
            # Sustain phase
            return 0.7
        else:
            # Release phase
            release_progress = (current_frame - (total_frames - release_frames)) / release_frames
            return 0.7 * (1.0 - release_progress)
    
    def play_key(self, key, duration=0.5):
        """Play a single key."""
        if key.upper() in self.key_frequencies:
            frequency = self.key_frequencies[key.upper()]
            tone = self.generate_tone(frequency, duration)
            
            # Convert numpy array to pygame sound
            sound = pygame.sndarray.make_sound(tone)
            sound.play()
            return sound
        return None
    
    def play_key_async(self, key, duration=0.5):
        """Play a key asynchronously."""
        def play():
            self.play_key(key, duration)
        
        thread = threading.Thread(target=play)
        thread.daemon = True
        thread.start()
        return thread
    
    def get_available_keys(self):
        """Return list of available keys."""
        return list(self.key_frequencies.keys())
    
    def cleanup(self):
        """Clean up pygame mixer."""
        pygame.mixer.quit()
